-- Complete Supabase setup for <PERSON><PERSON>'s Burger
-- Run this in Supabase SQL Editor

-- First, let's check what exists
SELECT 'Checking existing tables...' as status;

-- Drop existing tables if they exist (be careful with this in production!)
DROP TABLE IF EXISTS menu_items CASCADE;
DROP TABLE IF EXISTS blog_posts CASCADE;
DROP TABLE IF EXISTS content CASCADE;

-- Create menu_items table
CREATE TABLE menu_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_en TEXT,
    description TEXT,
    description_en TEXT,
    price DECIMAL(10,2),
    price_small DECIMAL(10,2),
    price_medium DECIMAL(10,2),
    price_large DECIMAL(10,2),
    category TEXT NOT NULL,
    menu_type TEXT NOT NULL CHECK (menu_type IN ('cafe', 'restaurant')),
    image_url TEXT,
    is_vegetarian BOOLEAN DEFAULT false,
    is_spicy BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    has_sizes BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_posts table
CREATE TABLE blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image_url TEXT,
    author TEXT NOT NULL,
    category TEXT NOT NULL,
    published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content table
CREATE TABLE content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    display_order INTEGER DEFAULT 0,
    section TEXT NOT NULL,
    page TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS for now (enable later with proper policies)
ALTER TABLE menu_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE content DISABLE ROW LEVEL SECURITY;

-- Insert sample menu items
INSERT INTO menu_items (name, description, category, menu_type, price, image_url, is_vegetarian, is_spicy, is_available) VALUES
('Klasik Cheeseburger', 'Sulu dana eti, eritilmiş cheddar peyniri, marul, domates ve özel sos', 'Burgerler', 'restaurant', 45.99, 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg', false, false, true),
('Truffle Patates', 'Truffle yağı, parmesan peyniri ve taze otlarla harmanlanmış çıtır patates', 'Yan Ürünler', 'restaurant', 28.99, 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg', true, false, true),
('BBQ Bacon Burger', 'Füme dana eti, çıtır bacon, cheddar ve ekşi BBQ sos', 'Burgerler', 'restaurant', 52.99, 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg', false, true, true),
('Oreo Milkshake', 'Oreo kurabiyeli kremalı vanilyalı milkshake, üzeri çırpılmış krema', 'İçecekler', 'cafe', 24.99, 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg', true, false, true),
('Veggie Burger', 'Ev yapımı bitki bazlı köfte, avokado, marul, domates ve vegan aioli', 'Burgerler', 'restaurant', 42.99, 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg', true, false, true);

-- Insert sample blog posts
INSERT INTO blog_posts (title, content, excerpt, author, category, published) VALUES
('Shebo''s Burger''ın Hikayesi', 'Shebo''s Burger, 2015 yılında küçük bir rüyayla başladı...', 'Markamızın kuruluş hikayesi ve bugünlere nasıl geldiğimiz', 'Shebo Team', 'Hakkımızda', true),
('En İyi Burger Nasıl Yapılır?', 'Mükemmel bir burger yapmak için gereken sırları sizlerle paylaşıyoruz...', 'Burger yapımının püf noktaları ve ipuçları', 'Chef Mehmet', 'Tarifler', true);

-- Insert sample content
INSERT INTO content (name, title, content, section, page) VALUES
('hero-title', 'Shebo''s Burger''a Hoş Geldiniz', 'En taze malzemelerle hazırlanan lezzetli burgerlerimizi keşfedin', 'hero', 'home'),
('hero-subtitle', 'Lezzet Tutkunları İçin', 'Her lokmada kalite ve lezzet garantisi', 'hero', 'home'),
('about-title', 'Hakkımızda', 'Shebo''s Burger olarak 2015 yılından beri müşterilerimize en kaliteli burgerleri sunuyoruz', 'about', 'home'),
('footer-address', 'Bostanlı Mahallesi, İzmir', 'Restoran adresi', 'footer', 'global'),
('footer-phone', '+90 232 123 45 67', 'İletişim telefonu', 'footer', 'global'),
('footer-email', '<EMAIL>', 'İletişim e-postası', 'footer', 'global');

-- Check if everything was created successfully
SELECT 'Tables created successfully!' as status;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('menu_items', 'blog_posts', 'content');
SELECT COUNT(*) as menu_items_count FROM menu_items;
SELECT COUNT(*) as blog_posts_count FROM blog_posts;
SELECT COUNT(*) as content_count FROM content;
