// Simple test to check Supabase connection
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://your-project-ref.supabase.co'
const supabaseKey = 'your-anon-key'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  try {
    console.log('Testing Supabase connection...')
    
    // Test basic connection
    const { data, error } = await supabase
      .from('menu_items')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.error('Database connection error:', error)
      return false
    }
    
    console.log('✅ Database connection successful!')
    console.log('Menu items count:', data)
    return true
    
  } catch (error) {
    console.error('❌ Connection failed:', error)
    return false
  }
}

testConnection()
