# 🚀 Deployment Guide for <PERSON><PERSON>'s Burger

## Option 1: Vercel (Recommended - Free)

### Step 1: Prepare for Deployment
1. Make sure all your changes are saved
2. Test the build locally:
   ```bash
   npm run build
   npm run preview
   ```

### Step 2: Deploy to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your GitHub repository
5. Configure environment variables:
   - `VITE_SUPABASE_URL`: https://vvwnzkpscijvpbiobykh.supabase.co
   - `VITE_SUPABASE_ANON_KEY`: your-anon-key
   - `VITE_SUPABASE_SERVICE_ROLE_KEY`: your-service-role-key
6. Click "Deploy"

### Step 3: Custom Domain (Optional)
1. In Vercel dashboard, go to your project
2. Go to "Settings" → "Domains"
3. Add your custom domain

---

## Option 2: Netlify (Alternative)

### Step 1: Build Settings
- Build command: `npm run build`
- Publish directory: `dist`

### Step 2: Environment Variables
Add the same environment variables as Vercel

---

## Option 3: Traditional Hosting (cPanel/FTP)

### Step 1: Build the Project
```bash
npm run build
```

### Step 2: Upload Files
1. Upload everything from the `dist` folder to your hosting's public_html
2. Configure environment variables in your hosting control panel

---

## Important Notes

### Security
- Never commit `.env` file to Git
- Use environment variables in production
- Consider using different Supabase projects for development/production

### Performance
- The build is optimized and minified
- Images are served from external CDNs
- Consider adding a CDN for better global performance

### Monitoring
- Set up error tracking (Sentry)
- Monitor performance (Vercel Analytics)
- Set up uptime monitoring

### SSL Certificate
- Vercel/Netlify provide free SSL
- For custom hosting, ensure SSL is enabled

### Database
- Your Supabase database is already configured
- No additional database setup needed
- Consider setting up proper RLS policies for production

## Post-Deployment Checklist

- [ ] Test all pages load correctly
- [ ] Test menu management in admin panel
- [ ] Test language switching
- [ ] Test contact forms
- [ ] Test mobile responsiveness
- [ ] Set up proper authentication for admin panel
- [ ] Configure proper RLS policies
- [ ] Set up backup strategy
- [ ] Configure monitoring and alerts
