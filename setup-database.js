// <PERSON>ript to set up the database with the provided SQL
import { createClient } from '@supabase/supabase-js'
import fs from 'fs'

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://vvwnzkpscijvpbiobykh.supabase.co'
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ2d256a3BzY2lqdnBiaW9ieWtoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzY4NjIsImV4cCI6MjA2Mzk1Mjg2Mn0.KSwbJzhZdifpEL1kT0QTcZq7H97czUasFk3NJnqNTmU'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    const { data, error } = await supabase
      .from('menu_items')
      .select('count(*)')
      .limit(1)
    
    if (error) {
      console.error('❌ Database connection error:', error.message)
      return false
    }
    
    console.log('✅ Database connection successful!')
    return true
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message)
    return false
  }
}

async function checkTables() {
  try {
    console.log('🔍 Checking database tables...')
    
    // Check menu_items table
    const { data: menuData, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .limit(5)
    
    if (menuError) {
      console.error('❌ Menu items table error:', menuError.message)
    } else {
      console.log('✅ Menu items table exists, found', menuData.length, 'items')
    }
    
    // Check blog_posts table
    const { data: blogData, error: blogError } = await supabase
      .from('blog_posts')
      .select('*')
      .limit(5)
    
    if (blogError) {
      console.error('❌ Blog posts table error:', blogError.message)
    } else {
      console.log('✅ Blog posts table exists, found', blogData.length, 'posts')
    }
    
    // Check content table
    const { data: contentData, error: contentError } = await supabase
      .from('content')
      .select('*')
      .limit(5)
    
    if (contentError) {
      console.error('❌ Content table error:', contentError.message)
    } else {
      console.log('✅ Content table exists, found', contentData.length, 'sections')
    }
    
  } catch (error) {
    console.error('❌ Error checking tables:', error.message)
  }
}

async function main() {
  console.log('🚀 Starting database setup check...\n')
  
  const connected = await testDatabaseConnection()
  if (!connected) {
    console.log('\n❌ Cannot proceed without database connection')
    return
  }
  
  console.log('')
  await checkTables()
  
  console.log('\n✅ Database setup check complete!')
  console.log('\n📝 Next steps:')
  console.log('1. Make sure you have run the SQL script in Supabase SQL Editor')
  console.log('2. Check that RLS policies are properly configured')
  console.log('3. Verify that the admin user has proper permissions')
}

main().catch(console.error)
