import { createClient } from '@supabase/supabase-js';

// Admin client with service role key for admin operations
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://vvwnzkpscijvpbiobykh.supabase.co';
const supabaseServiceKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Function to test admin connection
export const testAdminConnection = async () => {
  try {
    const { data, error } = await supabaseAdmin
      .from('menu_items')
      .select('count(*)')
      .limit(1);
    
    return { success: !error, error, data };
  } catch (err) {
    return { success: false, error: err, data: null };
  }
};
