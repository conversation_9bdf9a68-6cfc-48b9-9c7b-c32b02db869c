import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabaseClient';

const DatabaseTest = () => {
  const [status, setStatus] = useState<string>('Testing...');
  const [menuCount, setMenuCount] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testDatabase();
  }, []);

  const testDatabase = async () => {
    try {
      setStatus('Testing database connection...');
      
      // Test basic connection
      const { data, error } = await supabase
        .from('menu_items')
        .select('*', { count: 'exact' })
        .limit(1);
      
      if (error) {
        setError(`Database error: ${error.message}`);
        setStatus('Connection failed');
        return;
      }
      
      setMenuCount(data?.length || 0);
      setStatus('Connection successful!');
      setError(null);
      
    } catch (err) {
      setError(`Connection error: ${err}`);
      setStatus('Connection failed');
    }
  };

  const testInsert = async () => {
    try {
      setStatus('Testing insert...');
      
      const testItem = {
        name: 'Test Item',
        description: 'Test Description',
        category: 'Test',
        menu_type: 'restaurant',
        price: 10.99,
        is_available: true
      };
      
      const { data, error } = await supabase
        .from('menu_items')
        .insert([testItem])
        .select();
      
      if (error) {
        setError(`Insert error: ${error.message}`);
        setStatus('Insert failed');
        return;
      }
      
      setStatus('Insert successful!');
      setError(null);
      
      // Clean up - delete the test item
      if (data && data[0]) {
        await supabase
          .from('menu_items')
          .delete()
          .eq('id', data[0].id);
      }
      
    } catch (err) {
      setError(`Insert error: ${err}`);
      setStatus('Insert failed');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-bold mb-4">Database Connection Test</h3>
      
      <div className="space-y-4">
        <div>
          <strong>Status:</strong> <span className={status.includes('successful') ? 'text-green-600' : status.includes('failed') ? 'text-red-600' : 'text-blue-600'}>{status}</span>
        </div>
        
        <div>
          <strong>Menu Items Found:</strong> {menuCount}
        </div>
        
        {error && (
          <div className="text-red-600 bg-red-50 p-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}
        
        <div className="flex gap-3">
          <button
            onClick={testDatabase}
            className="btn bg-blue-600 text-white hover:bg-blue-700"
          >
            Test Connection
          </button>
          
          <button
            onClick={testInsert}
            className="btn bg-green-600 text-white hover:bg-green-700"
          >
            Test Insert
          </button>
        </div>
      </div>
    </div>
  );
};

export default DatabaseTest;
