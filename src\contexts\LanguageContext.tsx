import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'tr' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  tr: {
    // Navigation
    'nav.home': 'Ana Sayfa',
    'nav.menu': 'Menü',
    'nav.blog': 'Blog',
    'nav.contact': '<PERSON>let<PERSON><PERSON><PERSON>',
    'nav.admin': 'Yönetim',

    // Homepage
    'hero.title': 'Burger Mükemmelliğinin Tadını Çıkarın',
    'hero.subtitle': 'Unutulmaz bir lezzet deneyimi için premium malzemelerle el yapımı burgerler.',
    'hero.cta': '<PERSON>üyü Görüntüle',

    'about.title': 'Hikaye<PERSON>z',
    'about.content': 'Shebo\'s Burger 2015 yılında basit bir misyonla kuruldu: sadece en taze malzemeleri kullanarak en lezzetli, yüksek kaliteli burgerleri yaratmak. Küçük bir seyyar satış arabası olarak başlayan yolculuğumuz, sevilen yerel bir restorana dönüştü, ancak kalite ve lezzet konusundaki kararlılığımız hiç değişmedi.',

    'testimonials.title': 'Müşteri Yorumları',
    'testimonials.1.text': 'Şehirdeki en iyi burger! Malzemeler her zaman taze ve lezzet inanılmaz.',
    'testimonials.1.author': 'Ahmet Yılmaz',
    'testimonials.2.text': 'Harika atmosfer ve mükemmel servis. Kesinlikle tavsiye ederim!',
    'testimonials.2.author': 'Ayşe Demir',
    'testimonials.3.text': 'Vegetaryen seçenekleri de çok lezzetli. Herkese hitap eden bir yer.',
    'testimonials.3.author': 'Mehmet Kaya',

    'contact.title': 'İletişim & Konum',
    'contact.address': 'Bostanlı Mahallesi, İzmir',
    'contact.phone': '+90 232 123 45 67',
    'contact.email': '<EMAIL>',
    'contact.hours': 'Açılış Saatleri',
    'contact.hours.weekdays': 'Pazartesi - Cuma: 11:00 - 23:00',
    'contact.hours.weekend': 'Cumartesi - Pazar: 10:00 - 24:00',

    // Menu
    'menu.title': 'Menümüz',
    'menu.restaurant': 'Burger & Hotdog',
    'menu.cafe': 'Cafe',
    'menu.search': 'Menüde ara...',
    'menu.filter.all': 'Tümü',
    'menu.filter.vegetarian': 'Vejetaryen',
    'menu.filter.spicy': 'Acılı',
    'menu.currency': '₺',

    // Menu Categories
    'category.hamburgers': 'Hamburgerler',
    'category.chicken-burgers': 'Chicken Burgerler',
    'category.hot-dogs': 'Hot Doglar',
    'category.hot-coffees': 'Sıcak Kahveler',
    'category.brew-coffees': 'Demleme Kahveler',
    'category.cold-coffees': 'Soğuk Kahveler',
    'category.teas-drinks': 'Çaylar & Diğer İçecekler',
    'category.snacks': 'Tuzlu / Tatlı Atıştırmalıklar',

    // Blog
    'blog.title': 'Blog',
    'blog.readMore': 'Devamını Oku',
    'blog.shareOn': 'Paylaş:',
    'blog.category': 'Kategori',
    'blog.author': 'Yazar',
    'blog.date': 'Tarih',

    // Footer
    'footer.about': 'Hakkımızda',
    'footer.description': 'Shebo\'s Burger, 2015 yılından beri en taze malzemelerle en lezzetli burgerleri sunuyor. Kalite ve lezzet konusundaki kararlılığımız hiç değişmedi.',
    'footer.links': 'Hızlı Bağlantılar',
    'footer.hours': 'Açılış Saatleri',
    'footer.hours.weekdays': 'Pazartesi - Cuma',
    'footer.hours.saturday': 'Cumartesi',
    'footer.hours.sunday': 'Pazar',
    'footer.contact': 'İletişim',
    'footer.address': 'Bostanlı Mahallesi, İzmir',
    'footer.phone': '+90 232 123 45 67',
    'footer.email': '<EMAIL>',
    'footer.copyright': 'Tüm Hakları Saklıdır.',
    'footer.privacy': 'Gizlilik Politikası',
    'footer.terms': 'Kullanım Şartları',

    // Admin
    'admin.title': 'Yönetim Paneli',
    'admin.login': 'Giriş Yap',
    'admin.logout': 'Çıkış Yap',
    'admin.email': 'E-posta',
    'admin.password': 'Şifre',
    'admin.dashboard': 'Kontrol Paneli',
    'admin.menu': 'Menü Yönetimi',
    'admin.blog': 'Blog Yönetimi',
    'admin.content': 'İçerik Yönetimi',
    'admin.addNew': 'Yeni Ekle',
    'admin.edit': 'Düzenle',
    'admin.delete': 'Sil',
    'admin.save': 'Kaydet',
    'admin.cancel': 'İptal',
    'admin.loading': 'Yükleniyor...',
    'admin.welcome': 'Shebo\'s Burger yönetim paneline hoş geldiniz.',
    'admin.stats.menuItems': 'Menü Öğeleri',
    'admin.stats.blogPosts': 'Blog Yazıları',
    'admin.stats.contentSections': 'İçerik Bölümleri',
    'admin.quickActions': 'Hızlı İşlemler',
    'admin.manageMenu': 'Menü Yönet',
    'admin.manageMenu.desc': 'Menü öğelerini ve kategorilerini ekle, düzenle veya sil',
    'admin.manageBlog': 'Blog Yönet',
    'admin.manageBlog.desc': 'Blog yazıları oluştur ve düzenle',
    'admin.manageContent': 'İçerik Yönet',
    'admin.manageContent.desc': 'Ana sayfa ve diğer içerik bölümlerini güncelle',
    'admin.devTools': 'Geliştirme Araçları',
    'admin.signOut': 'Çıkış Yap',
    'admin.goTo': 'Git',
    'admin.viewWebsite': 'Web Sitesini Görüntüle',

    // Menu Management
    'admin.menu.title': 'Menü Yönetimi',
    'admin.menu.subtitle': 'Menü öğelerini ekle, düzenle ve sil',
    'admin.menu.addItem': 'Öğe Ekle',
    'admin.menu.editItem': 'Menü Öğesini Düzenle',
    'admin.menu.addNewItem': 'Yeni Menü Öğesi Ekle',
    'admin.menu.restaurant': 'Restoran',
    'admin.menu.cafe': 'Kafe',
    'admin.menu.search': 'Menü öğelerini ara...',
    'admin.menu.noItems': 'Menü öğesi bulunamadı. Farklı bir arama deneyin veya yeni öğe ekleyin.',
    'admin.menu.loading': 'Menü öğeleri yükleniyor...',
    'admin.menu.name': 'Ad',
    'admin.menu.category': 'Kategori',
    'admin.menu.price': 'Fiyat',
    'admin.menu.status': 'Durum',
    'admin.menu.actions': 'İşlemler',
    'admin.menu.available': 'Mevcut',
    'admin.menu.unavailable': 'Mevcut Değil',
    'admin.menu.description': 'Açıklama',
    'admin.menu.menuType': 'Menü Türü',
    'admin.menu.imageUrl': 'Resim URL',
    'admin.menu.vegetarian': 'Vejetaryen',
    'admin.menu.spicy': 'Acılı',
    'admin.menu.deleteConfirm': 'Bu menü öğesini silmek istediğinizden emin misiniz?',
    'admin.menu.saveFailed': 'Menü öğesi kaydedilemedi. Lütfen tekrar deneyin.',
    'admin.menu.deleteFailed': 'Menü öğesi silinemedi. Lütfen tekrar deneyin.',

    // Common
    'common.loading': 'Yükleniyor...',
    'common.error': 'Bir hata oluştu',
    'common.success': 'Başarılı',
    'common.name': 'Ad',
    'common.description': 'Açıklama',
    'common.price': 'Fiyat',
    'common.category': 'Kategori',
    'common.image': 'Resim',
    'common.size': 'Boyut',
    'common.small': 'Küçük',
    'common.medium': 'Orta',
    'common.large': 'Büyük',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.menu': 'Menu',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',
    'nav.admin': 'Admin',

    // Homepage
    'hero.title': 'Indulge in Burger Perfection',
    'hero.subtitle': 'Handcrafted burgers made with premium ingredients for an unforgettable taste experience.',
    'hero.cta': 'View Menu',

    'about.title': 'Our Story',
    'about.content': 'Shebo\'s Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.',

    'testimonials.title': 'Customer Reviews',
    'testimonials.1.text': 'Best burger in town! The ingredients are always fresh and the flavor is incredible.',
    'testimonials.1.author': 'John Smith',
    'testimonials.2.text': 'Amazing atmosphere and perfect service. Definitely recommend!',
    'testimonials.2.author': 'Sarah Johnson',
    'testimonials.3.text': 'The vegetarian options are also very delicious. A place for everyone.',
    'testimonials.3.author': 'Mike Wilson',

    'contact.title': 'Contact & Location',
    'contact.address': 'Bostanlı District, İzmir',
    'contact.phone': '+90 232 123 45 67',
    'contact.email': '<EMAIL>',
    'contact.hours': 'Opening Hours',
    'contact.hours.weekdays': 'Monday - Friday: 11:00 - 23:00',
    'contact.hours.weekend': 'Saturday - Sunday: 10:00 - 24:00',

    // Menu
    'menu.title': 'Our Menu',
    'menu.restaurant': 'Burger & Hotdog',
    'menu.cafe': 'Cafe',
    'menu.search': 'Search menu...',
    'menu.filter.all': 'All',
    'menu.filter.vegetarian': 'Vegetarian',
    'menu.filter.spicy': 'Spicy',
    'menu.currency': '₺',

    // Menu Categories
    'category.hamburgers': 'Hamburgers',
    'category.chicken-burgers': 'Chicken Burgers',
    'category.hot-dogs': 'Hot Dogs',
    'category.hot-coffees': 'Hot Coffees',
    'category.brew-coffees': 'Brew Coffees',
    'category.cold-coffees': 'Cold Coffees',
    'category.teas-drinks': 'Teas & Other Drinks',
    'category.snacks': 'Salty / Sweet Snacks',

    // Blog
    'blog.title': 'Blog',
    'blog.readMore': 'Read More',
    'blog.shareOn': 'Share on:',
    'blog.category': 'Category',
    'blog.author': 'Author',
    'blog.date': 'Date',

    // Footer
    'footer.about': 'About Us',
    'footer.description': 'Shebo\'s Burger has been serving the most delicious burgers with the freshest ingredients since 2015. Our commitment to quality and flavor has never wavered.',
    'footer.links': 'Quick Links',
    'footer.hours': 'Opening Hours',
    'footer.hours.weekdays': 'Monday - Friday',
    'footer.hours.saturday': 'Saturday',
    'footer.hours.sunday': 'Sunday',
    'footer.contact': 'Contact Us',
    'footer.address': 'Bostanlı District, İzmir',
    'footer.phone': '+90 232 123 45 67',
    'footer.email': '<EMAIL>',
    'footer.copyright': 'All Rights Reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',

    // Admin
    'admin.title': 'Admin Dashboard',
    'admin.login': 'Login',
    'admin.logout': 'Logout',
    'admin.email': 'Email',
    'admin.password': 'Password',
    'admin.dashboard': 'Dashboard',
    'admin.menu': 'Menu Management',
    'admin.blog': 'Blog Management',
    'admin.content': 'Content Management',
    'admin.addNew': 'Add New',
    'admin.edit': 'Edit',
    'admin.delete': 'Delete',
    'admin.save': 'Save',
    'admin.cancel': 'Cancel',
    'admin.loading': 'Loading...',
    'admin.welcome': 'Welcome to Shebo\'s Burger admin panel.',
    'admin.stats.menuItems': 'Menu Items',
    'admin.stats.blogPosts': 'Blog Posts',
    'admin.stats.contentSections': 'Content Sections',
    'admin.quickActions': 'Quick Actions',
    'admin.manageMenu': 'Manage Menu',
    'admin.manageMenu.desc': 'Add, edit, or delete menu items and categories',
    'admin.manageBlog': 'Manage Blog',
    'admin.manageBlog.desc': 'Create and edit blog posts',
    'admin.manageContent': 'Manage Content',
    'admin.manageContent.desc': 'Update homepage and other content sections',
    'admin.devTools': 'Development Tools',
    'admin.signOut': 'Sign Out',
    'admin.goTo': 'Go to',
    'admin.viewWebsite': 'View Website',

    // Menu Management
    'admin.menu.title': 'Menu Management',
    'admin.menu.subtitle': 'Add, edit, and delete menu items',
    'admin.menu.addItem': 'Add Item',
    'admin.menu.editItem': 'Edit Menu Item',
    'admin.menu.addNewItem': 'Add New Menu Item',
    'admin.menu.restaurant': 'Restaurant',
    'admin.menu.cafe': 'Café',
    'admin.menu.search': 'Search menu items...',
    'admin.menu.noItems': 'No menu items found. Try a different search or add a new item.',
    'admin.menu.loading': 'Loading menu items...',
    'admin.menu.name': 'Name',
    'admin.menu.category': 'Category',
    'admin.menu.price': 'Price',
    'admin.menu.status': 'Status',
    'admin.menu.actions': 'Actions',
    'admin.menu.available': 'Available',
    'admin.menu.unavailable': 'Unavailable',
    'admin.menu.description': 'Description',
    'admin.menu.menuType': 'Menu Type',
    'admin.menu.imageUrl': 'Image URL',
    'admin.menu.vegetarian': 'Vegetarian',
    'admin.menu.spicy': 'Spicy',
    'admin.menu.deleteConfirm': 'Are you sure you want to delete this menu item?',
    'admin.menu.saveFailed': 'Failed to save menu item. Please try again.',
    'admin.menu.deleteFailed': 'Failed to delete menu item. Please try again.',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.name': 'Name',
    'common.description': 'Description',
    'common.price': 'Price',
    'common.category': 'Category',
    'common.image': 'Image',
    'common.size': 'Size',
    'common.small': 'Small',
    'common.medium': 'Medium',
    'common.large': 'Large',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('tr'); // Default to Turkish

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'tr' || savedLanguage === 'en')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
