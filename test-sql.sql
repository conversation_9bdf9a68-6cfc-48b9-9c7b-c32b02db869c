-- Test SQL queries to check database structure and data

-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('menu_items', 'blog_posts', 'content');

-- Check menu_items table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'menu_items' 
ORDER BY ordinal_position;

-- Check if there are any menu items
SELECT COUNT(*) as total_menu_items FROM menu_items;

-- Check sample menu items
SELECT id, name, category, price, menu_type, is_available 
FROM menu_items 
LIMIT 5;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('menu_items', 'blog_posts', 'content');

-- Test insert (this will help us see if <PERSON><PERSON> is blocking)
-- INSERT INTO menu_items (name, description, category, menu_type, price, is_available) 
-- VALUES ('Test Item', 'Test Description', 'Test Category', 'restaurant', 10.99, true);
