import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Plus, Edit, Trash2, X, Search } from 'lucide-react';
import { supabase, MenuItem } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import { useLanguage } from '../../contexts/LanguageContext';

type MenuType = 'cafe' | 'restaurant';
type FormData = Omit<MenuItem, 'id' | 'created_at'>;

const AdminMenu = () => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [currentItem, setCurrentItem] = useState<MenuItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeType, setActiveType] = useState<MenuType>('restaurant');
  const [categories, setCategories] = useState<string[]>([]);
  const [showForm, setShowForm] = useState(false);
  const { t } = useLanguage();

  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<FormData>();

  useEffect(() => {
    fetchMenuItems();
  }, []);

  // Fetch menu items
  const fetchMenuItems = async () => {
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .order('name');

      if (error) throw error;

      if (data) {
        setMenuItems(data);

        // Extract unique categories
        const uniqueCategories = [...new Set(data.map(item => item.category))];
        setCategories(uniqueCategories);
      }
    } catch (error) {
      console.error('Error fetching menu items:', error);

      // Use default data if database fetch fails
      const defaultItems: MenuItem[] = [
        {
          id: '1',
          name: 'Classic Cheeseburger',
          description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
          price: 12.99,
          category: 'Burgers',
          menu_type: 'restaurant',
          image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg',
          is_vegetarian: false,
          is_spicy: false,
          is_available: true,
          created_at: ''
        },
        {
          id: '2',
          name: 'Truffle Fries',
          description: 'Crispy fries tossed with truffle oil, parmesan cheese, and fresh herbs',
          price: 8.99,
          category: 'Sides',
          menu_type: 'restaurant',
          image_url: 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg',
          is_vegetarian: true,
          is_spicy: false,
          is_available: true,
          created_at: ''
        },
        {
          id: '3',
          name: 'BBQ Bacon Burger',
          description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
          price: 14.99,
          category: 'Burgers',
          menu_type: 'restaurant',
          image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg',
          is_vegetarian: false,
          is_spicy: true,
          is_available: true,
          created_at: ''
        },
        {
          id: '4',
          name: 'Oreo Milkshake',
          description: 'Creamy vanilla milkshake blended with Oreo cookies and topped with whipped cream',
          price: 6.99,
          category: 'Drinks',
          menu_type: 'cafe',
          image_url: 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg',
          is_vegetarian: true,
          is_spicy: false,
          is_available: true,
          created_at: ''
        }
      ];

      setMenuItems(defaultItems);

      // Extract unique categories
      const uniqueCategories = [...new Set(defaultItems.map(item => item.category))];
      setCategories(uniqueCategories);
    } finally {
      setLoading(false);
    }
  };

  // Filter items when search, type, or items change
  useEffect(() => {
    let result = menuItems;

    // Filter by menu type
    result = result.filter(item => item.menu_type === activeType);

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        item =>
          item.name.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.category.toLowerCase().includes(query)
      );
    }

    setFilteredItems(result);
  }, [menuItems, activeType, searchQuery]);

  // Handle add/edit form submission
  const onSubmit = async (data: FormData) => {
    try {
      if (isEditing && currentItem) {
        // Update existing item
        const { error } = await supabase
          .from('menu_items')
          .update(data)
          .eq('id', currentItem.id);

        if (error) throw error;

        // Update local state
        setMenuItems(prev =>
          prev.map(item =>
            item.id === currentItem.id
              ? { ...item, ...data }
              : item
          )
        );
      } else {
        // Add new item
        const { data: newItem, error } = await supabase
          .from('menu_items')
          .insert([data])
          .select();

        if (error) throw error;

        // Update local state
        if (newItem) {
          setMenuItems(prev => [...prev, ...newItem]);

          // Add new category if it doesn't exist
          if (!categories.includes(data.category)) {
            setCategories(prev => [...prev, data.category]);
          }
        }
      }

      // Reset form and close
      closeForm();
    } catch (error) {
      console.error('Error saving menu item:', error);
      alert(t('admin.menu.saveFailed'));
    }
  };

  // Delete menu item
  const handleDelete = async (id: string) => {
    if (!window.confirm(t('admin.menu.deleteConfirm'))) {
      return;
    }

    try {
      const { error } = await supabase
        .from('menu_items')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setMenuItems(prev => prev.filter(item => item.id !== id));
    } catch (error) {
      console.error('Error deleting menu item:', error);
      alert(t('admin.menu.deleteFailed'));
    }
  };

  // Open form for editing
  const handleEdit = (item: MenuItem) => {
    setCurrentItem(item);
    setIsEditing(true);

    // Set form values
    setValue('name', item.name);
    setValue('description', item.description);
    setValue('price', item.price);
    setValue('category', item.category);
    setValue('menu_type', item.menu_type);
    setValue('image_url', item.image_url || '');
    setValue('is_vegetarian', item.is_vegetarian || false);
    setValue('is_spicy', item.is_spicy || false);
    setValue('is_available', item.is_available);

    setShowForm(true);
  };

  // Open form for adding
  const handleAdd = () => {
    setCurrentItem(null);
    setIsEditing(false);

    // Reset form with defaults
    reset({
      name: '',
      description: '',
      price: 0,
      category: '',
      menu_type: activeType,
      image_url: '',
      is_vegetarian: false,
      is_spicy: false,
      is_available: true,
    });

    setShowForm(true);
  };

  // Close form
  const closeForm = () => {
    setShowForm(false);
    setCurrentItem(null);
    setIsEditing(false);
    reset();
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-2">{t('admin.menu.title')}</h1>
          <p className="text-gray-600">{t('admin.menu.subtitle')}</p>
        </div>

        <button
          onClick={handleAdd}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          {t('admin.menu.addItem')}
        </button>
      </div>

      {/* Menu Type and Search */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Menu Type Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1 w-full md:w-auto">
            <button
              className={`px-4 py-2 rounded-md text-lg font-medium transition-colors ${
                activeType === 'restaurant'
                  ? 'bg-coffee text-white'
                  : 'text-coffee hover:bg-gray-200'
              }`}
              onClick={() => setActiveType('restaurant')}
            >
              {t('admin.menu.restaurant')}
            </button>
            <button
              className={`px-4 py-2 rounded-md text-lg font-medium transition-colors ${
                activeType === 'cafe'
                  ? 'bg-coffee text-white'
                  : 'text-coffee hover:bg-gray-200'
              }`}
              onClick={() => setActiveType('cafe')}
            >
              {t('admin.menu.cafe')}
            </button>
          </div>

          {/* Search */}
          <div className="relative w-full md:w-1/3">
            <input
              type="text"
              placeholder={t('admin.menu.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-coffee"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          </div>
        </div>
      </div>

      {/* Menu Items Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {loading ? (
          <div className="p-6 text-center">
            <p>{t('admin.menu.loading')}</p>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="p-6 text-center">
            <p>{t('admin.menu.noItems')}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.menu.name')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.menu.category')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.menu.price')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.menu.status')}</th>
                  <th className="px-6 py-3 text-coffee font-semibold">{t('admin.menu.actions')}</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {item.image_url && (
                          <img
                            src={item.image_url}
                            alt={item.name}
                            className="h-10 w-10 rounded-md object-cover mr-3"
                          />
                        )}
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">{item.category}</td>
                    <td className="px-6 py-4">${item.price.toFixed(2)}</td>
                    <td className="px-6 py-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        item.is_available
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {item.is_available ? t('admin.menu.available') : t('admin.menu.unavailable')}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleEdit(item)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">
                {isEditing ? t('admin.menu.editItem') : t('admin.menu.addNewItem')}
              </h2>
              <button
                onClick={closeForm}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="form-label">{t('admin.menu.name')}</label>
                  <input
                    {...register('name', { required: t('admin.menu.name') + ' is required' })}
                    className="form-input"
                    placeholder="e.g., Classic Cheeseburger"
                  />
                  {errors.name && (
                    <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="form-label">{t('admin.menu.description')}</label>
                  <textarea
                    {...register('description', { required: t('admin.menu.description') + ' is required' })}
                    className="form-input h-24"
                    placeholder="Describe this menu item..."
                  />
                  {errors.description && (
                    <p className="text-red-600 text-sm mt-1">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">{t('admin.menu.category')}</label>
                  <input
                    {...register('category', { required: t('admin.menu.category') + ' is required' })}
                    className="form-input"
                    placeholder="e.g., Burgers, Sides, Drinks"
                    list="categories"
                  />
                  <datalist id="categories">
                    {categories.map((category) => (
                      <option key={category} value={category} />
                    ))}
                  </datalist>
                  {errors.category && (
                    <p className="text-red-600 text-sm mt-1">{errors.category.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">{t('admin.menu.price')} (₺)</label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('price', {
                      required: t('admin.menu.price') + ' is required',
                      min: { value: 0, message: t('admin.menu.price') + ' must be positive' }
                    })}
                    className="form-input"
                    placeholder="0.00"
                  />
                  {errors.price && (
                    <p className="text-red-600 text-sm mt-1">{errors.price.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">{t('admin.menu.menuType')}</label>
                  <select
                    {...register('menu_type')}
                    className="form-input"
                  >
                    <option value="restaurant">{t('admin.menu.restaurant')}</option>
                    <option value="cafe">{t('admin.menu.cafe')}</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">{t('admin.menu.imageUrl')}</label>
                  <input
                    {...register('image_url')}
                    className="form-input"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div className="md:col-span-2 flex flex-wrap gap-6">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_vegetarian"
                      {...register('is_vegetarian')}
                      className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                    />
                    <label htmlFor="is_vegetarian" className="ml-2 text-coffee">
                      {t('admin.menu.vegetarian')}
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_spicy"
                      {...register('is_spicy')}
                      className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                    />
                    <label htmlFor="is_spicy" className="ml-2 text-coffee">
                      {t('admin.menu.spicy')}
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_available"
                      {...register('is_available')}
                      className="h-4 w-4 text-coffee focus:ring-coffee rounded"
                    />
                    <label htmlFor="is_available" className="ml-2 text-coffee">
                      {t('admin.menu.available')}
                    </label>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={closeForm}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  {t('admin.cancel')}
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  {isEditing ? t('admin.edit') : t('admin.addNew')}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AdminMenu;